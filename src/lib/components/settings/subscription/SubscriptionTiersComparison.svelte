<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import type { SubscriptionTier } from '$lib/types/subscription';
	import { Button, Badge } from 'flowbite-svelte';
	import {
		ShieldSolid,
		CheckOutline,
		CloseOutline,
		StarSolid
	} from 'flowbite-svelte-icons';

	export let tiers: SubscriptionTier[];
	export let currentTier: string;

	const dispatch = createEventDispatcher();

	// Icon mapping
	const iconMap = {
		Shield: ShieldSolid,
		Zap: ShieldSolid, // Using ShieldSolid as fallback
		Crown: ShieldSolid // Using ShieldSolid as fallback
	};

	// Color mapping
	const colorMap = {
		blue: 'blue',
		purple: 'purple',
		gold: 'yellow'
	};

	function getIcon(iconName: string) {
		return iconMap[iconName as keyof typeof iconMap] || ShieldSolid;
	}

	function getColorClasses(color: string, isPopular: boolean = false) {
		const baseColor = colorMap[color as keyof typeof colorMap] || 'blue';
		
		if (isPopular) {
			return {
				border: `border-${baseColor}-500 ring-2 ring-${baseColor}-200`,
				header: `bg-gradient-to-r from-${baseColor}-500 to-${baseColor}-600 text-white`,
				button: `bg-${baseColor}-600 hover:bg-${baseColor}-700 text-white`,
				badge: `bg-${baseColor}-100 text-${baseColor}-800`
			};
		}
		
		return {
			border: 'border-gray-200',
			header: 'bg-gray-50 text-gray-900',
			button: `bg-${baseColor}-600 hover:bg-${baseColor}-700 text-white`,
			badge: `bg-${baseColor}-100 text-${baseColor}-800`
		};
	}

	function formatFeatureValue(value: string | boolean | number): { display: string; isAvailable: boolean } {
		if (typeof value === 'boolean') {
			return { display: value ? 'Yes' : 'No', isAvailable: value };
		}
		if (typeof value === 'number') {
			return { display: value.toString(), isAvailable: value > 0 };
		}
		if (value === 'false' || value === '0') {
			return { display: 'Not Available', isAvailable: false };
		}
		return { display: value.toString(), isAvailable: true };
	}

	function handleUpgrade(tier: SubscriptionTier) {
		dispatch('action', { 
			action: 'upgrade', 
			data: { targetTier: tier.id, tierName: tier.name } 
		});
	}

	function isCurrentTier(tierName: string): boolean {
		return tierName === currentTier;
	}

	// Get key features for comparison
	const keyFeatures = [
		'Active User Accounts',
		'Storage',
		'AI Workflow (unit)',
		'IT Support',
		'Smart Reply',
		'Memory',
		'SLA Configuration'
	];
</script>

<div class="space-y-6">
	<div class="text-center mb-8">
		<h2 class="text-2xl font-bold text-gray-900 mb-2">
			{t('choose_your_plan') || 'Choose Your Plan'}
		</h2>
		<p class="text-gray-600">
			{t('plan_comparison_description') || 'Compare features and choose the plan that best fits your needs'}
		</p>
	</div>

	<!-- Tier Cards Grid -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		{#each tiers as tier}
			{@const colorClasses = getColorClasses(tier.color, tier.popular)}
			{@const IconComponent = getIcon(tier.icon)}
			{@const isCurrent = isCurrentTier(tier.name)}
			
			<div class="relative bg-white rounded-lg shadow-md border-2 {colorClasses.border} {tier.popular ? 'transform scale-105' : ''}">
				<!-- Popular Badge -->
				{#if tier.popular}
					<div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
						<Badge class="px-3 py-1 {colorClasses.badge} border">
							<StarSolid class="w-3 h-3 mr-1" />
							{t('most_popular') || 'Most Popular'}
						</Badge>
					</div>
				{/if}

				<!-- Current Plan Badge -->
				{#if isCurrent}
					<div class="absolute -top-3 right-4">
						<Badge color="green" class="px-3 py-1">
							{t('current_plan') || 'Current Plan'}
						</Badge>
					</div>
				{/if}

				<!-- Header -->
				<div class="p-6 {colorClasses.header} rounded-t-lg">
					<div class="flex items-center justify-center mb-4">
						<div class="p-3 bg-white bg-opacity-20 rounded-lg">
							<IconComponent class="h-8 w-8" />
						</div>
					</div>
					<h3 class="text-xl font-bold text-center">{tier.name}</h3>
					<p class="text-center opacity-90 mt-2">{tier.description}</p>
					
					<!-- Pricing -->
					<div class="text-center mt-4">
						<span class="text-3xl font-bold">{tier.price}</span>
						<span class="text-lg opacity-75">{tier.period}</span>
					</div>
				</div>

				<!-- Features -->
				<div class="p-6">
					<!-- Highlights -->
					<div class="mb-6">
						<h4 class="font-semibold text-gray-900 mb-3">
							{t('key_features') || 'Key Features'}
						</h4>
						<ul class="space-y-2">
							{#each tier.highlights as highlight}
								<li class="flex items-center text-sm text-gray-700">
									<CheckOutline class="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
									{highlight}
								</li>
							{/each}
						</ul>
					</div>

					<!-- Key Feature Comparison -->
					<div class="mb-6">
						<h4 class="font-semibold text-gray-900 mb-3">
							{t('feature_details') || 'Feature Details'}
						</h4>
						<div class="space-y-2">
							{#each keyFeatures as featureName}
								{@const featureValue = tier.features[featureName]}
								{@const formatted = formatFeatureValue(featureValue)}
								
								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-600">{featureName}:</span>
									<div class="flex items-center">
										{#if formatted.isAvailable}
											<CheckOutline class="h-3 w-3 text-green-500 mr-1" />
											<span class="text-gray-900 font-medium">{formatted.display}</span>
										{:else}
											<CloseOutline class="h-3 w-3 text-gray-400 mr-1" />
											<span class="text-gray-400">{formatted.display}</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					</div>

					<!-- Action Button -->
					<div class="mt-6">
						{#if isCurrent}
							<Button 
								color="alternative" 
								class="w-full"
								disabled
							>
								{t('current_plan') || 'Current Plan'}
							</Button>
						{:else}
							<Button 
								class="w-full {colorClasses.button}"
								on:click={() => handleUpgrade(tier)}
							>
								{tier.name === 'Premium' && currentTier === 'Basic' 
									? (t('upgrade_to') || 'Upgrade to') + ` ${tier.name}`
									: tier.name !== 'Premium' 
									? (t('upgrade_to') || 'Upgrade to') + ` ${tier.name}`
									: (t('select_plan') || 'Select Plan')
								}
							</Button>
						{/if}
					</div>
				</div>
			</div>
		{/each}
	</div>

	<!-- Feature Comparison Table -->
	<div class="bg-white rounded-lg shadow-md border p-6 mt-8">
		<h3 class="text-lg font-semibold text-gray-900 mb-6">
			{t('detailed_comparison') || 'Detailed Feature Comparison'}
		</h3>
		
		<div class="overflow-x-auto">
			<table class="min-w-full divide-y divide-gray-200">
				<thead class="bg-gray-50">
					<tr>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
							{t('feature') || 'Feature'}
						</th>
						{#each tiers as tier}
							<th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
								{tier.name}
								{#if isCurrentTier(tier.name)}
									<Badge color="green" class="ml-2 text-xs">Current</Badge>
								{/if}
							</th>
						{/each}
					</tr>
				</thead>
				<tbody class="bg-white divide-y divide-gray-200">
					{#each Object.keys(tiers[0].features) as featureName}
						<tr class="hover:bg-gray-50">
							<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
								{featureName}
							</td>
							{#each tiers as tier}
								{@const featureValue = tier.features[featureName]}
								{@const formatted = formatFeatureValue(featureValue)}
								
								<td class="px-6 py-4 whitespace-nowrap text-center text-sm">
									{#if formatted.isAvailable}
										<div class="flex items-center justify-center">
											<CheckOutline class="h-4 w-4 text-green-500 mr-1" />
											<span class="text-gray-900">{formatted.display}</span>
										</div>
									{:else}
										<div class="flex items-center justify-center">
											<CloseOutline class="h-4 w-4 text-gray-400 mr-1" />
											<span class="text-gray-400">{formatted.display}</span>
										</div>
									{/if}
								</td>
							{/each}
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</div>
</div>
