<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { FeatureCategory, SubscriptionTier } from '$lib/types/subscription';
	import {
		UsersSolid,
		DatabaseSolid,
		ClockSolid,
		CheckOutline,
		CloseOutline,
		InfoCircleSolid
	} from 'flowbite-svelte-icons';

	export let categories: FeatureCategory[];
	export let tiers: SubscriptionTier[];
	export let currentTier: string;

	// Icon mapping for categories
	const categoryIconMap = {
		Users: UsersSolid,
		Bot: UsersSolid, // Using UsersSolid as fallback
		Database: DatabaseSolid,
		MessageSquare: UsersSolid, // Using UsersSolid as fallback
		BarChart3: UsersSolid, // Using UsersSolid as fallback
		Clock: ClockSolid
	};

	function getCategoryIcon(iconName: string) {
		return categoryIconMap[iconName as keyof typeof categoryIconMap] || InfoCircleSolid;
	}

	function formatFeatureValue(value: string | boolean | number): { display: string; isAvailable: boolean } {
		if (typeof value === 'boolean') {
			return { display: value ? 'Yes' : 'No', isAvailable: value };
		}
		if (typeof value === 'number') {
			return { display: value.toString(), isAvailable: value > 0 };
		}
		if (value === 'false' || value === '0') {
			return { display: 'Not Available', isAvailable: false };
		}
		return { display: value.toString(), isAvailable: true };
	}

	// Remove unused function
	// function getCurrentTierIndex(): number {
	// 	return tiers.findIndex(tier => tier.name === currentTier);
	// }

	function getTierColor(tierName: string): string {
		const tier = tiers.find(t => t.name === tierName);
		const colorMap = {
			blue: 'blue',
			purple: 'purple',
			gold: 'yellow'
		};
		return colorMap[tier?.color as keyof typeof colorMap] || 'blue';
	}

	// Remove unused variable
	// $: currentTierIndex = getCurrentTierIndex();
</script>

<div class="space-y-6">
	<div class="text-center mb-8">
		<h2 class="text-2xl font-bold text-gray-900 mb-2">
			{t('feature_categories') || 'Feature Categories'}
		</h2>
		<p class="text-gray-600">
			{t('feature_categories_description') || 'Explore features organized by category across all subscription tiers'}
		</p>
	</div>

	<!-- Categories Grid -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		{#each categories as category}
			{@const IconComponent = getCategoryIcon(category.icon)}
			
			<div class="bg-white rounded-lg shadow-md border p-6">
				<!-- Category Header -->
				<div class="flex items-center space-x-3 mb-4">
					<div class="p-2 bg-blue-100 rounded-lg">
						<IconComponent class="h-6 w-6 text-blue-600" />
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900">{category.name}</h3>
						<p class="text-sm text-gray-600">{category.description}</p>
					</div>
				</div>

				<!-- Features in Category -->
				<div class="space-y-3">
					{#each category.features as featureName}
						<div class="border border-gray-200 rounded-lg p-4">
							<h4 class="font-medium text-gray-900 mb-3">{featureName}</h4>
							
							<!-- Feature comparison across tiers -->
							<div class="grid grid-cols-3 gap-3">
								{#each tiers as tier}
									{@const featureValue = tier.features[featureName]}
									{@const formatted = formatFeatureValue(featureValue)}
									{@const tierColor = getTierColor(tier.name)}
									{@const isCurrentTier = tier.name === currentTier}
									
									<div class="text-center p-3 rounded-lg border {isCurrentTier ? `bg-${tierColor}-50 border-${tierColor}-200` : 'bg-gray-50 border-gray-200'}">
										<div class="flex items-center justify-center mb-2">
											<span class="text-xs font-medium text-gray-600">{tier.name}</span>
											{#if isCurrentTier}
												<span class="ml-1 px-1.5 py-0.5 bg-{tierColor}-100 text-{tierColor}-800 text-xs rounded-full">
													Current
												</span>
											{/if}
										</div>
										
										<div class="flex items-center justify-center space-x-1">
											{#if formatted.isAvailable}
												<CheckOutline class="h-4 w-4 text-green-500" />
												<span class="text-sm font-medium text-gray-900">{formatted.display}</span>
											{:else}
												<CloseOutline class="h-4 w-4 text-gray-400" />
												<span class="text-sm text-gray-400">{formatted.display}</span>
											{/if}
										</div>
									</div>
								{/each}
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/each}
	</div>

	<!-- Feature Availability Matrix -->
	<div class="bg-white rounded-lg shadow-md border p-6 mt-8">
		<h3 class="text-lg font-semibold text-gray-900 mb-6">
			{t('feature_availability_matrix') || 'Feature Availability Matrix'}
		</h3>
		
		<div class="overflow-x-auto">
			<table class="min-w-full divide-y divide-gray-200">
				<thead class="bg-gray-50">
					<tr>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
							{t('category') || 'Category'}
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
							{t('feature') || 'Feature'}
						</th>
						{#each tiers as tier}
							{@const tierColor = getTierColor(tier.name)}
							{@const isCurrentTier = tier.name === currentTier}
							
							<th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
								<div class="flex items-center justify-center space-x-2">
									<span>{tier.name}</span>
									{#if isCurrentTier}
										<span class="px-2 py-1 bg-{tierColor}-100 text-{tierColor}-800 text-xs rounded-full">
											Current
										</span>
									{/if}
								</div>
							</th>
						{/each}
					</tr>
				</thead>
				<tbody class="bg-white divide-y divide-gray-200">
					{#each categories as category}
						{#each category.features as featureName, featureIndex}
							<tr class="hover:bg-gray-50">
								{#if featureIndex === 0}
									<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" rowspan={category.features.length}>
										<div class="flex items-center space-x-2">
											<svelte:component this={getCategoryIcon(category.icon)} class="h-4 w-4 text-gray-600" />
											<span>{category.name}</span>
										</div>
									</td>
								{/if}
								
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{featureName}
								</td>
								
								{#each tiers as tier}
									{@const featureValue = tier.features[featureName]}
									{@const formatted = formatFeatureValue(featureValue)}
									{@const isCurrentTier = tier.name === currentTier}
									
									<td class="px-6 py-4 whitespace-nowrap text-center text-sm {isCurrentTier ? 'bg-blue-50' : ''}">
										{#if formatted.isAvailable}
											<div class="flex items-center justify-center space-x-1">
												<CheckOutline class="h-4 w-4 text-green-500" />
												<span class="text-gray-900 font-medium">{formatted.display}</span>
											</div>
										{:else}
											<div class="flex items-center justify-center space-x-1">
												<CloseOutline class="h-4 w-4 text-gray-400" />
												<span class="text-gray-400">{formatted.display}</span>
											</div>
										{/if}
									</td>
								{/each}
							</tr>
						{/each}
					{/each}
				</tbody>
			</table>
		</div>
	</div>

	<!-- Feature Highlights -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
		{#each tiers as tier}
			{@const tierColor = getTierColor(tier.name)}
			{@const isCurrentTier = tier.name === currentTier}
			
			<div class="bg-white rounded-lg shadow-md border p-6 {isCurrentTier ? `ring-2 ring-${tierColor}-200` : ''}">
				<div class="flex items-center justify-between mb-4">
					<h4 class="text-lg font-semibold text-gray-900">{tier.name}</h4>
					{#if isCurrentTier}
						<span class="px-2 py-1 bg-{tierColor}-100 text-{tierColor}-800 text-xs rounded-full font-medium">
							Current Plan
						</span>
					{/if}
				</div>
				
				<div class="space-y-2">
					<p class="text-2xl font-bold text-gray-900">{tier.price}<span class="text-lg font-normal text-gray-600">{tier.period}</span></p>
					<p class="text-sm text-gray-600 mb-4">{tier.description}</p>
					
					<div class="space-y-2">
						{#each tier.highlights as highlight}
							<div class="flex items-center space-x-2">
								<CheckOutline class="h-4 w-4 text-green-500 flex-shrink-0" />
								<span class="text-sm text-gray-700">{highlight}</span>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
	</div>
</div>
