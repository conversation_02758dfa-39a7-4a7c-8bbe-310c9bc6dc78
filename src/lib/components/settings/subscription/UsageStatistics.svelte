<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { SubscriptionQuotaData } from '$lib/api/types/subscription-api';
	import {
		CloseCircleSolid,
		CheckCircleSolid
	} from 'flowbite-svelte-icons';

	export let quotaData: SubscriptionQuotaData;

	// Calculate usage percentages and status
	function calculateUsage(current: number | string, limit: string | number | boolean): {
		percentage: number;
		status: 'good' | 'warning' | 'critical';
		display: string;
	} {
		if (typeof limit === 'boolean' || limit === 'Unlimited') {
			return {
				percentage: 0,
				status: 'good',
				display: limit === 'Unlimited' ? 'Unlimited' : limit ? 'Available' : 'Not Available'
			};
		}

		const currentNum = typeof current === 'string' ? parseFloat(current) : current;
		const limitNum = typeof limit === 'string' ? parseFloat(limit) : limit;

		if (isNaN(currentNum) || isNaN(limitNum) || limitNum === 0) {
			return { percentage: 0, status: 'good', display: 'N/A' };
		}

		const percentage = (currentNum / limitNum) * 100;
		let status: 'good' | 'warning' | 'critical' = 'good';

		if (percentage >= 90) status = 'critical';
		else if (percentage >= 75) status = 'warning';

		return {
			percentage: Math.min(percentage, 100),
			status,
			display: `${currentNum} / ${limitNum}`
		};
	}

	// Usage items configuration based on API data
	$: usageItems = [
		{
			key: 'users',
			label: t('subscription_item_active_users'),
			current: quotaData.quota.current_active_users,
			limit: quotaData.quota.max_active_users,
		},
		{
			key: 'line_accounts',
			label: t('subscription_item_line_accounts'),
			current: quotaData.quota.current_line_accounts,
			limit: quotaData.quota.max_line_accounts,
		},
		{
			key: 'ai_workflows',
			label: t('subscription_item_ai_workflow_units'),
			current: quotaData.quota.current_ai_workflow_units,
			limit: quotaData.quota.max_ai_workflow_units,
		},
		{
			key: 'messages',
			label: t('subscription_item_messages_per_minute'),
			current: 0, // TODO
			limit: quotaData.quota.max_messages_per_min,
		},
		{
			key: 'storage',
			label: t('subscription_item_storage_limit'),
			current: quotaData.quota.current_storage_gb,
			limit: quotaData.quota.max_storage_gb,
			isStorage: true,
			unit: 'GB'
		}
	];

	function getStatusColor(status: string): string {
		switch (status) {
			case 'critical': return 'text-red-600 bg-red-100';
			case 'warning': return 'text-yellow-600 bg-yellow-100';
			default: return 'text-green-600 bg-green-100';
		}
	}

	function getProgressBarColor(status: string): string {
		switch (status) {
			case 'critical': return 'bg-red-500';
			case 'warning': return 'bg-yellow-500';
			default: return 'bg-green-500';
		}
	}

	function calculateStorageUsage(current: number, limit: string | number | boolean) {
		if (typeof limit === 'boolean' || limit === 'Unlimited') {
			return {
				percentage: 0,
				status: 'good' as const,
				display: limit === 'Unlimited' ? 'Unlimited' : 'N/A'
			};
		}

		// const currentGB = parseStorageValue(current);
		// const limitGB = typeof limit === 'string' ? parseStorageValue(limit) : limit;
		const currentGB = current;
		const limitGB = limit;

		if (limitGB === 0) {
			return { percentage: 0, status: 'good' as const, display: 'N/A' };
		}

		const percentage = (currentGB / limitGB) * 100;
		let status: 'good' | 'warning' | 'critical' = 'good';

		if (percentage >= 90) status = 'critical';
		else if (percentage >= 75) status = 'warning';

		return {
			percentage: Math.min(percentage, 100),
			status,
			display: `${current} / ${limit}`
		};
	}

	// Format feature names for display
	function formatFeatureName(featureKey: string): string {
		const featureNames: Record<string, string> = {
			custom_transfer_algo: 'Custom Transfer Algorithm',
			custom_case_desc: 'Custom Case Description',
			custom_ai_workflow: 'Custom AI Workflow',
			ai_quick_reply: 'AI Quick Reply',
			ai_smart_reply: 'AI Smart Reply',
			ai_memory: 'AI Memory',
			crm_integration: 'CRM Integration',
			crm_notify_claim: 'CRM Notify Claim',
			crm_case_system: 'CRM Case System',
			dashboard_sla_config: 'Dashboard SLA Config',
			dashboard_sla_alert: 'Dashboard SLA Alert',
			broadcasting: 'Broadcasting'
		};
		return featureNames[featureKey] || featureKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
	}
</script>

<div class="space-y-6">
	<div class="bg-white rounded-lg shadow-md border p-6">
		<div class="flex items-center justify-between mb-6">
			<div>
				<h2 class="text-xl font-semibold text-gray-900">
					{t('subscription_usage_stats')}
				</h2>
			</div>
		</div>

		<!-- Usage Cards Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
			{#each usageItems as item}
				{@const usage = item.isStorage ? 
					calculateStorageUsage(item.current, item.limit) : 
					calculateUsage(item.current, item.limit)}
				
				<div class="bg-gray-50 rounded-lg p-4 border">
					<div class="flex items-center justify-between mb-3">
						<div class="flex items-center space-x-2">
							<h3 class="font-medium text-gray-900 text-sm">{item.label}</h3>
						</div>					
					</div>

					<!-- Usage Display -->
					<div class="space-y-2">
						<div class="flex items-center justify-between">
							<span class="text-lg font-semibold text-gray-900">
								{item.current.toLocaleString()}
								{#if item.unit}
									{item.unit}
								{/if}
							</span>
							<span class="text-xs px-2 py-1 rounded-full {getStatusColor(usage.status)}">
								{typeof item.limit === 'string' && item.limit === 'unlimited' ? 
									t('subscription_item_unlimited') :
								 	usage.percentage.toFixed(0) + '%'
								}
							</span>
						</div>

						{#if typeof item.limit !== 'string' || item.limit !== 'unlimited'}
							<!-- {#if usage.percentage > 0} -->
								<!-- Progress Bar -->
								<div class="w-full bg-gray-200 rounded-full h-2">
									<div
										class="h-2 rounded-full transition-all duration-300 {getProgressBarColor(usage.status)}"
										style="width: {usage.percentage}%"
									></div>
								</div>
							<!-- {/if} -->

							<p class="text-xs text-gray-600">
								{item.current} / {item.limit}
								{#if item.unit}
									{item.unit}
								{/if}
							</p>
						{:else}
							<p class="text-xs text-gray-600">Unlimited</p>
						{/if}
					</div>
				</div>
			{/each}
		</div>

		<!-- Available Features Section -->
		<div class="mt-8 pt-6 border-t border-gray-200">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">
				{t('subscription_all_features')}
			</h3>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{#each Object.entries(quotaData.features) as [featureKey, isEnabled]}
					<div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
						{#if isEnabled}
							<CheckCircleSolid class="h-5 w-5 text-green-500 flex-shrink-0" />
						{:else}
							<CloseCircleSolid class="h-5 w-5 text-gray-400 flex-shrink-0" />
						{/if}
						<div>
							<p class="text-sm font-medium text-gray-900">
								{formatFeatureName(featureKey)}
							</p>
							<p class="text-xs text-gray-500">
								{isEnabled ? t('subscription_feature_available') : t('subscription_feature_not_available')}
							</p>
						</div>
					</div>
				{/each}
			</div>
		</div>
	</div>
</div>
