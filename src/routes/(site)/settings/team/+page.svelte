<script lang="ts">
	// +page.svelte
	import { t } from '$lib/stores/i18n';
	import type { PageData } from './$types';
	import { writable } from 'svelte/store';
	import { onMount, afterUpdate } from 'svelte';
	import {
		// Breadcrumb,
		// BreadcrumbItem,
		Input,
		Label,
		Button,
		Toggle,
		Secondary,
		Textarea,
		Toast
	} from 'flowbite-svelte';
	import { CloseCircleSolid, CheckCircleSolid, CheckOutline } from 'flowbite-svelte-icons';
	import { enhance } from '$app/forms';
	import { fly } from 'svelte/transition';

	import CompanySection from '$src/lib/components/settings/business/CompanySection.svelte';
	import ChatbotSection from '$src/lib/components/settings/business/ChatbotSection.svelte';
	import ConnectionSection from '$src/lib/components/settings/business/ConnectionSection.svelte';
	import WebsiteSection from '$src/lib/components/settings/business/Systemection.svelte';
	import ProductSection from '$src/lib/components/settings/business/ProductSection.svelte';
	import UserSection from '$src/lib/components/settings/business/UserSection.svelte';
	import CustomerSection from '$src/lib/components/settings/business/CustomerSection.svelte';
	import TicketSection from '$src/lib/components/settings/business/TicketSection.svelte';
	import BusinessHour from '$src/lib/components/settings/business/BusinessHour.svelte';
	import QuickResponseTemplate from '$src/lib/components/settings/business/QuickResponseTemplate.svelte';

	export let data: PageData;

	// Store original data for reference
	let originalData = structuredClone(data);

	// Toast state for feedback
	let toastMessage = 'Successfully';
	let toastStatus = false;
	let counter = 3;

	// Active tab with persistence - initialize as empty string
	let activeTab = '';
	let pageTitle = 'Team Management';
	let lastActiveTab = '';
	let settingsForm;

	// Available tabs configuration
	const availableTabs = ['user', 'customer', 'transfer', 'business_hours', 'responses_template'];
	const defaultTab = 'user';
	const TAB_STORAGE_KEY = 'team_management_active_tab';

	// Create stores for each section with proper initialization
	const systemSettings = writable({
		logo: data.system_setting.COMPANY_LOGO,
		dominant_color: data.system_setting.DOMINANT_COLOR,
		secondary_color: data.system_setting.SECONDARY_COLOR,
		accent_color: data.system_setting.ACCENT_COLOR,
		inactive_1st: data.system_setting.INACTIVE_TICKET_1ST_TIME_MINUTES,
		inactive_2nd: data.system_setting.INACTIVE_TICKET_2ND_TIME_MINUTES
	});

	const botSettings = writable({
		thaiName: data.system_setting.CHATBOT_MASCOT_THAI_NAME,
		englishName: data.system_setting.CHATBOT_MASCOT_ENGLISH_NAME,
		role: data.system_setting.CHATBOT_ROLE,
		gender: data.system_setting.CHATBOT_GENDER,
		conversationStyle: data.system_setting.CHATBOT_CONVERSATION_STYLE,
		conversationType: data.system_setting.CHATBOT_CONVERSATION_TYPE
	});

	const companySettings = writable({
		thaiName: data.system_setting.COMPANY_THAI_NAME,
		englishName: data.system_setting.COMPANY_ENGLISH_NAME,
		business: data.system_setting.COMPANY_BUSINESS,
		businessType: data.system_setting.COMPANY_BUSINESS_TYPE
	});

	const connectionSettings = writable({
		lineChannelSecret: data.system_setting.LINE_CHANNEL_SECRET,
		lineAccessToken: data.system_setting.LINE_ACCESS_TOKEN,
		lineWebhook: data.system_setting.LINE_WEBHOOK,
		lineOAQRCode: data.system_setting.LINE_OA_QR_CODE,
		lineOAQRLink: data.system_setting.LINE_OA_QR_LINK,
		lineGroupQRCode: data.system_setting.LINE_GROUP_QR_CODE,
		lineGroupQRLink: data.system_setting.LINE_GROUP_QR_LINK
	});

	const transferSettings = writable({
		transfer_ticket_partner: data.system_setting.AUTO_TRANSFER_TICKET_PARTNER_CONDITION,
		transfer_ticket_department: data.system_setting.AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION,
		transfer_ticket_tag: data.system_setting.AUTO_TRANSFER_TICKET_USER_TAG_CONDITION
	});

	const businessHourSettings = writable({
		business_hour: data.system_setting.COMPANY_BUSINESS_HOURS
	});

	// Chatbot behavior outside business hours state management
	let chatbotOutsideHoursMessage = data.system_setting?.COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS || '';
	let originalChatbotOutsideHoursMessage = data.system_setting?.COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS || '';


	// Computed rows: count line breaks + 1
  	$: rows = Math.max(1, chatbotOutsideHoursMessage.split('\n').length);

	// Reactive declaration to detect changes in chatbot outside hours message
	$: chatbotMessageChanged = chatbotOutsideHoursMessage !== originalChatbotOutsideHoursMessage;

	// Form reference for chatbot outside hours message
	let chatbotOutsideHoursForm: HTMLFormElement;

	// Reactive declarations for filtered data
	$: partnerNames = data.partners.filter((partner) => partner.is_default === false);
	$: departmentNames = data.departments;
	$: userTagNames = data.user_tags;
	$: customerTagNames = data.customer_tags;
	$: productList = {};

	// Function to save active tab to storage
	function saveActiveTab(tab) {
		if (typeof window !== 'undefined') {
			try {
				window.sessionStorage.setItem(TAB_STORAGE_KEY, tab);
			} catch (e) {
				console.warn('Failed to save active tab to sessionStorage:', e);
			}
		}
	}

	// Function to load active tab from storage
	function loadActiveTab() {
		if (typeof window !== 'undefined') {
			try {
				const savedTab = window.sessionStorage.getItem(TAB_STORAGE_KEY);
				return savedTab && availableTabs.includes(savedTab) ? savedTab : defaultTab;
			} catch (e) {
				console.warn('Failed to load active tab from sessionStorage:', e);
				return defaultTab;
			}
		}
		return defaultTab;
	}

	// Function to initialize active tab
	function initializeActiveTab() {
		const savedTab = loadActiveTab();
		activeTab = savedTab;
		// console.log('Initialized active tab:', activeTab);
	}

	// Function to refresh all stores from data
	function refreshAllStores() {
		// console.log('Refreshing all stores with latest data');

		systemSettings.set({
			logo: data.system_setting.COMPANY_LOGO,
			dominant_color: data.system_setting.DOMINANT_COLOR,
			secondary_color: data.system_setting.SECONDARY_COLOR,
			accent_color: data.system_setting.ACCENT_COLOR,
			inactive_1st: data.system_setting.INACTIVE_TICKET_1ST_TIME_MINUTES,
			inactive_2nd: data.system_setting.INACTIVE_TICKET_2ND_TIME_MINUTES
		});

		botSettings.set({
			thaiName: data.system_setting.CHATBOT_MASCOT_THAI_NAME,
			englishName: data.system_setting.CHATBOT_MASCOT_ENGLISH_NAME,
			role: data.system_setting.CHATBOT_ROLE,
			gender: data.system_setting.CHATBOT_GENDER,
			conversationStyle: data.system_setting.CHATBOT_CONVERSATION_STYLE,
			conversationType: data.system_setting.CHATBOT_CONVERSATION_TYPE
		});

		companySettings.set({
			thaiName: data.system_setting.COMPANY_THAI_NAME,
			englishName: data.system_setting.COMPANY_ENGLISH_NAME,
			business: data.system_setting.COMPANY_BUSINESS,
			businessType: data.system_setting.COMPANY_BUSINESS_TYPE
		});

		connectionSettings.set({
			lineChannelSecret: data.system_setting.LINE_CHANNEL_SECRET,
			lineAccessToken: data.system_setting.LINE_ACCESS_TOKEN,
			lineWebhook: data.system_setting.LINE_WEBHOOK,
			lineOAQRCode: data.system_setting.LINE_OA_QR_CODE,
			lineOAQRLink: data.system_setting.LINE_OA_QR_LINK,
			lineGroupQRCode: data.system_setting.LINE_GROUP_QR_CODE,
			lineGroupQRLink: data.system_setting.LINE_GROUP_QR_LINK
		});

		transferSettings.set({
			transfer_ticket_partner: data.system_setting.AUTO_TRANSFER_TICKET_PARTNER_CONDITION,
			transfer_ticket_department: data.system_setting.AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION,
			transfer_ticket_tag: data.system_setting.AUTO_TRANSFER_TICKET_USER_TAG_CONDITION
		});

		businessHourSettings.set({
			business_hour: data.system_setting.COMPANY_BUSINESS_HOURS
		});

		// Update chatbot outside hours message state
		chatbotOutsideHoursMessage = data.system_setting?.COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS || '';
		originalChatbotOutsideHoursMessage = data.system_setting?.COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS || '';
	}

	// Function to save chatbot outside hours message
	function saveChatbotOutsideHoursMessage() {
		const settings = [{ key: 'COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS', value: chatbotOutsideHoursMessage }];

		const settingsInput = chatbotOutsideHoursForm.querySelector(
			'input[name="settings"]'
		) as HTMLInputElement;
		if (settingsInput) {
			settingsInput.value = JSON.stringify(settings);
		}

		// toastMessage = 'Chatbot outside hours message saved successfully!';
		// toastStatus = true;
		// counter = 3;
		// timeout();

		chatbotOutsideHoursForm.requestSubmit();
	}

	// Function to handle tab changes with refresh and persistence
	function handleTabChange(newTab) {
		if (activeTab !== newTab && availableTabs.includes(newTab)) {
			lastActiveTab = activeTab;
			activeTab = newTab;

			// Save the new active tab
			saveActiveTab(newTab);

			// Refresh the stores when switching tabs
			refreshAllStores();

			// Let the component know we've switched back to it
			dispatchEvent(
				new CustomEvent('tab-changed', {
					detail: { previousTab: lastActiveTab, currentTab: newTab }
				})
			);

			// console.log('Tab changed from', lastActiveTab, 'to', newTab);
		}
	}

	// Listen for settings updates from child components
	function handleSettingsUpdated(event) {
		// Refresh stores when settings are updated
		refreshAllStores();

		// Show toast notification
		// toastMessage = 'Settings updated successfully!';
		// toastStatus = true;
		// counter = 3;
		// timeout();
	}

	// Toast timeout method
	function timeout() {
		if (counter > 0 && toastStatus) {
			setTimeout(() => {
				counter--;
				timeout();
			}, 1000);
		} else {
			toastStatus = false;
		}
	}

	// Initialize everything on mount
	onMount(() => {
		// Initialize active tab first
		initializeActiveTab();

		// Then refresh stores
		refreshAllStores();

		// Set up listener for settings updates
		window.addEventListener('settings-updated', handleSettingsUpdated);

		return () => {
			window.removeEventListener('settings-updated', handleSettingsUpdated);
		};
	});

	// Reactively update data when it changes from the server
	$: if (data && data.system_setting) {
		refreshAllStores();
	}
	let hourSettings = JSON.parse(data.system_setting.COMPANY_BUSINESS_HOURS);
</script>

<svelte:head>
	<title>{t('teamManagement')}</title>
</svelte:head>

{#if toastStatus}
	<Toast
		color="green"
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus
		class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
	>
		<CheckCircleSolid slot="icon" class="h-5 w-5" />
		{toastMessage}
	</Toast>
{/if}

<div class="min-h-screen rounded-lg bg-white">
	<div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
		<!-- <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 bg-white rounded-lg"> -->

		<!-- <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
			<BreadcrumbItem href="/" home>
				<span class="text-gray-400">{t('home')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem>
				<span class="text-gray-400">{t('settings')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem>
				<span class="text-gray-700">{t('team_management')}</span>
			</BreadcrumbItem>
		</Breadcrumb> -->

		<div class="mb-6">
			<h2 class="text-2xl font-bold">{t('team_management')}</h2>
			<p class="text-gray-600">{t('team_management_description')}</p>
		</div>

		<!-- <div class="bg-white rounded-lg shadow overflow-hidden"> -->
		<div class="rounded-lg border-b bg-white">
			<!-- Navigation Tabs -->
			<div class="flex border-b">
				<button
					id="settings-team-user-tab"
					class="border-b-2 px-6 py-4 text-sm font-medium {activeTab !== 'user'
						? 'text-gray-500 hover:text-gray-700'
						: ''}"
					style={activeTab === 'user'
						? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
						: ''}
					on:click={() => handleTabChange('user')}
				>
					{t('tab_user')}
				</button>

				<button
					id="settings-team-customer-tab"
					class="border-b-2 px-6 py-4 text-sm font-medium {activeTab !== 'customer'
						? 'text-gray-500 hover:text-gray-700'
						: ''}"
					style={activeTab === 'customer'
						? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
						: ''}
					on:click={() => handleTabChange('customer')}
				>
					{t('tab_customer')}
				</button>

				<button
					id="settings-team-transfer-tab"
					class="border-b-2 px-6 py-4 text-sm font-medium {activeTab !== 'transfer'
						? 'text-gray-500 hover:text-gray-700'
						: ''}"
					style={activeTab === 'transfer'
						? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
						: ''}
					on:click={() => handleTabChange('transfer')}
				>
					{t('tab_transfer')}
				</button>

				<button
					id="settings-team-business-hours-tab"
					class="border-b-2 px-6 py-4 text-sm font-medium {activeTab !== 'business_hours'
						? 'text-gray-500 hover:text-gray-700'
						: ''}"
					style={activeTab === 'business_hours'
						? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
						: ''}
					on:click={() => handleTabChange('business_hours')}
				>
					{t('business_hours')}
				</button>

				<!-- Temporary disabled for now -->
				<!-- <button
					class="border-b-2 px-6 py-4 text-sm font-medium {activeTab !== 'responses_template'
						? 'text-gray-500 hover:text-gray-700'
						: ''}"
					style={activeTab === 'responses_template'
						? `color: ${$systemSettings.dominant_color}; border-color: ${$systemSettings.dominant_color};`
						: ''}
					on:click={() => handleTabChange('responses_template')}
				>
					{t('responses_template')}
				</button> -->
			</div>
		</div>

		<!-- Content -->
		<div>
			<!-- User Settings -->
			{#if activeTab === 'user'}
				<UserSection
					{partnerNames}
					{departmentNames}
					{userTagNames}
					on:settings-updated={handleSettingsUpdated}
				/>
			{/if}

			<!-- Customer Settings -->
			{#if activeTab === 'customer'}
				<CustomerSection {customerTagNames} on:settings-updated={handleSettingsUpdated} />
			{/if}

			<!-- Transfer Settings -->
			{#if activeTab === 'transfer'}
				<TicketSection {transferSettings} on:settings-updated={handleSettingsUpdated} />
			{/if}

			<!-- Transfer Settings -->
			{#if activeTab === 'business_hours'}
				<BusinessHour
					businessHourSettings={hourSettings}
					on:settings-updated={handleSettingsUpdated}
				/>

				<div class="mt-6 space-y-4 rounded-lg bg-white p-6 shadow-md">
					<div class="flex w-full items-center justify-between">
						<div>
							<h2 class="text-xl font-medium text-gray-700">
								{t('chatbot_behavior_outside_business_hours')}
							</h2>
							<p class="text-sm text-gray-600">{t('configure_automatic_message_outside_hours')}</p>
						</div>
						<Button
							id="settings-team-chatbot-outside-hours-save"
							type="button"
							color="green"
							disabled={!chatbotMessageChanged}
							class="inline-flex items-center rounded-md border border-transparent text-sm font-medium shadow-sm disabled:cursor-not-allowed disabled:opacity-20"
							on:click={saveChatbotOutsideHoursMessage}
						>
							<CheckOutline class="mr-2 h-4 w-4" />
							{t('save')}
						</Button>
					</div>

					<Textarea
						id="settings-team-chatbot-outside-hours-message"
						placeholder={t('auto_notification_message_placeholder')}
						rows={rows}
						bind:value={chatbotOutsideHoursMessage}
					/>
					<p class="dark:text-gray-400 ms-auto text-xs text-gray-500">
						{t('automatic_message_outside_hours_description')}
					</p>
				</div>

				<!-- Hidden form for chatbot outside hours message submission -->
				<form
					id="settings-team-chatbot-outside-hours-form"
					bind:this={chatbotOutsideHoursForm}
					action="?/update_system_setting"
					method="POST"
					use:enhance={() => {
						return async ({ result }) => {
							// Update original value after successful save
							originalChatbotOutsideHoursMessage = chatbotOutsideHoursMessage;

							const { toastStore } = await import('$lib/stores/toastStore');
							if(result.type === 'failure') {
								toastStore.add(t('business_hours_out_of_hours_message_failure'), 'error');
							}
							else if (result.type === 'success') {
								toastStore.add(t('business_hours_out_of_hours_message_success'), 'success');
							}

							// toastMessage = 'Chatbot outside hours message saved successfully!';
							// toastStatus = true;
							// counter = 3;
							// timeout();
						};
					}}
					class="hidden"
				>
					<input type="hidden" name="settings" value="" />
				</form>
			{/if}

			<!-- Responses Template -->
			<!-- {#if activeTab === 'responses_template'}
				<QuickResponseTemplate />
				<QuickResponseTemplate {templates} on:settings-updated={handleSettingsUpdated}/>
			{/if} -->
		</div>
	</div>
</div>