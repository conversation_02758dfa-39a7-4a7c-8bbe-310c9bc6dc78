{"currentSubscription": {"tier": "Premium", "status": "active", "expiresAt": "2024-12-31", "serialNumber": "SLM-PREM-2024-001"}, "tiers": [{"id": "premium", "name": "Premium", "price": "$99", "period": "/month", "description": "Perfect for small teams getting started", "icon": "Shield", "popular": false, "color": "blue", "features": {"Active User Accounts": "5", "Transfer Algorithm": "Round-Robin", "Case Description": "Predefined", "Quick Reply": true, "Smart Reply": false, "Memory": false, "Reprint API": false, "Display Customer Policy": false, "Notify Claim Status": false, "Case System": false, "LINE Integration": "5", "AI Workflow (unit)": "1", "Workflow Management": "Fixed Flow", "SLA Configuration": false, "SLA Anomaly Alert": false, "Broadcast": true, "Message/Minutes": "30", "Storage": "250 GB", "IT Support": "Working hour"}, "highlights": ["5 Active User Accounts", "250 GB Storage", "Basic AI Assistant", "Working Hours Support"]}, {"id": "enterprise", "name": "Enterprise", "price": "$299", "period": "/month", "description": "Advanced features for growing businesses", "icon": "Zap", "popular": true, "color": "purple", "features": {"Active User Accounts": "20", "Transfer Algorithm": "Customization", "Case Description": "Customization", "Quick Reply": true, "Smart Reply": true, "Memory": true, "Reprint API": true, "Display Customer Policy": true, "Notify Claim Status": false, "Case System": false, "LINE Integration": "10", "AI Workflow (unit)": "5", "Workflow Management": "Customize flow (+ IT Support)", "SLA Configuration": true, "SLA Anomaly Alert": false, "Broadcast": true, "Message/Minutes": "100", "Storage": "1 TB", "IT Support": "24/7"}, "highlights": ["20 Active User Accounts", "1 TB Storage", "Advanced AI with Memory", "24/7 Support", "Custom Workflows"]}, {"id": "enterprise-plus", "name": "Enterprise Plus", "price": "$599", "period": "/month", "description": "Complete solution for enterprise organizations", "icon": "Crown", "popular": false, "color": "gold", "features": {"Active User Accounts": "50", "Transfer Algorithm": "Customization", "Case Description": "Customization", "Quick Reply": true, "Smart Reply": true, "Memory": true, "Reprint API": true, "Display Customer Policy": true, "Notify Claim Status": true, "Case System": true, "LINE Integration": "Unlimited", "AI Workflow (unit)": "Unlimited", "Workflow Management": "Customize flow (+ IT Support)", "SLA Configuration": true, "SLA Anomaly Alert": true, "Broadcast": true, "Message/Minutes": "200", "Storage": "2 TB", "IT Support": "24/7"}, "highlights": ["50 Active User Accounts", "2 TB Storage", "Unlimited AI Workflows", "Complete Case System", "SLA Monitoring"]}], "featureCategories": [{"id": "ticket-management", "name": "Ticket Management & Agent Management", "icon": "Users", "description": "Manage support tickets and agent workflows efficiently", "features": ["Active User Accounts", "Transfer Algorithm", "Case Description"]}, {"id": "ai-assistant", "name": "AI Assistant", "icon": "Bot", "description": "Intelligent automation and response capabilities", "features": ["Quick Reply", "Smart Reply", "Memory"]}, {"id": "crm-integration", "name": "CRM Integration", "icon": "Database", "description": "Customer relationship management and data integration", "features": ["Reprint API", "Display Customer Policy", "Notify Claim Status", "Case System"]}, {"id": "chatbot-management", "name": "Chatbot Logic Management & Integration", "icon": "MessageSquare", "description": "Advanced chatbot workflows and platform integrations", "features": ["LINE Integration", "AI Workflow (unit)", "Workflow Management"]}, {"id": "dashboard", "name": "Dashboard", "icon": "BarChart3", "description": "Analytics, monitoring, and performance tracking", "features": ["SLA Configuration", "SLA Anomaly Alert"]}, {"id": "social-application", "name": "Social Application", "icon": "MessageSquare", "description": "Social media and broadcast messaging capabilities", "features": ["Broadcast"]}, {"id": "service-capability", "name": "Service Capability", "icon": "Clock", "description": "Core service limits and support options", "features": ["Message/Minutes", "Storage", "IT Support"]}], "styles": {"colors": {"primary": "#2563eb", "secondary": "#64748b", "accent": "#7c3aed", "success": "#16a34a", "warning": "#ea580c", "error": "#dc2626", "background": "#ffffff", "foreground": "#0f172a", "muted": "#64748b", "card": "#f8fafc", "border": "#e2e8f0"}, "typography": {"fontFamily": {"sans": ["Inter", "system-ui", "sans-serif"], "mono": ["JetBrains Mono", "monospace"]}, "fontSize": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem"}}, "spacing": {"xs": "0.5rem", "sm": "1rem", "md": "1.5rem", "lg": "2rem", "xl": "3rem"}, "borderRadius": {"sm": "0.375rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem"}}, "ui": {"components": {"card": {"className": "rounded-lg border bg-card text-card-foreground shadow-sm", "variants": {"default": "border-border", "highlighted": "ring-2 ring-primary", "current": "bg-primary/5 border-primary/20"}}, "button": {"className": "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors", "variants": {"primary": "bg-primary text-primary-foreground hover:bg-primary/90", "secondary": "bg-secondary text-secondary-foreground hover:bg-secondary/80", "outline": "border border-input hover:bg-accent hover:text-accent-foreground"}}, "badge": {"className": "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold", "variants": {"default": "bg-primary text-primary-foreground", "secondary": "bg-secondary text-secondary-foreground", "success": "bg-green-100 text-green-800", "warning": "bg-yellow-100 text-yellow-800"}}}}, "mockData": {"activationCodes": ["SLM-PREM-2024-001", "SLM-ENT-2024-002", "SLM-PLUS-2024-003"], "usageStats": {"currentUsers": 3, "storageUsed": "45 GB", "messagesThisMonth": 1250, "workflowsActive": 1}, "notifications": [{"type": "info", "message": "Your subscription expires in 30 days", "timestamp": "2024-12-01T10:00:00Z"}, {"type": "success", "message": "Serial number activated successfully", "timestamp": "2024-11-15T14:30:00Z"}]}}